import streamlit as st
from ultralytics import YOLO
from PIL import Image
import time
import os

# Configuration
MODEL_PATH = r"C:\Users\<USER>\project\Image_test\best.pt"
# Page configuration
st.set_page_config(
    page_title="Road Detection Classifier",
    page_icon="�️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .unknown-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .road-detected {
        background-color: #d1f2eb;
        border: 2px solid #27ae60;
        color: #0e6b3a;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        text-align: center;
        font-weight: bold;
    }
    .no-road-detected {
        background-color: #fdeaea;
        border: 2px solid #e74c3c;
        color: #a93226;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        text-align: center;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_model(model_path):
    """Load YOLOv11 classification model"""
    try:
        if not os.path.exists(model_path):
            return None, f"Model file not found at {model_path}"

        model = YOLO(model_path)

        # Verify it's a classification model
        if hasattr(model, 'task') and model.task != 'classify':
            st.warning(f"⚠️ Model task is '{model.task}', expected 'classify'")

        return model, None
    except Exception as e:
        return None, str(e)

def preprocess_image(image):
    """Preprocess image for YOLOv11 classification"""
    try:
        # YOLOv11 classification models can handle PIL images directly
        # No need for complex preprocessing
        return image, None
    except Exception as e:
        return None, str(e)

def perform_classification_inference(model, image, confidence_threshold, debug_mode=False):
    """
    Perform YOLOv11 SINGLE-CLASS classification inference for road detection

    This function is designed for single-class models trained ONLY on road images.
    Logic:
    - High confidence on road class → ROAD
    - Low confidence on road class → I DON'T KNOW (uncertain)
    - Any unexpected prediction → I DON'T KNOW (not trained for this)

    Args:
        model: YOLOv11 classification model (single-class: road only)
        image: PIL Image
        confidence_threshold: Minimum confidence for road classification (0.7-0.8 recommended)
        debug_mode: Whether to show debug information

    Returns:
        dict: Classification results with strict thresholding
    """
    try:
        if debug_mode:
            st.write("� **YOLOv11 Classification Debug Info:**")
            st.write(f"- Model type: {type(model)}")
            st.write(f"- Model names: {getattr(model, 'names', 'No names attribute')}")
            st.write(f"- Model task: {getattr(model, 'task', 'Unknown')}")
            st.write(f"- Image size: {image.size}")
            st.write(f"- Confidence threshold: {confidence_threshold}")
            st.write(f"- **SINGLE-CLASS MODEL**: Trained only on road images")
            st.write(f"- **Logic**: High confidence road → ROAD, Low confidence → I DON'T KNOW")

        # Perform classification inference
        results = model(image, verbose=False)

        if debug_mode:
            st.write(f"- Results type: {type(results)}")
            st.write(f"- Results length: {len(results) if results else 'None'}")

        if not results or len(results) == 0:
            return {
                'success': False,
                'classification': 'i_dont_know',
                'confidence': 0.0,
                'class_name': 'no_result',
                'class_id': -1,
                'reason': 'No results returned from model - treating as unknown',
                'error': 'Model returned empty results'
            }

        result = results[0]

        if debug_mode:
            st.write(f"- Result type: {type(result)}")
            st.write(f"- Result attributes: {[attr for attr in dir(result) if not attr.startswith('_')]}")

        # Handle classification results
        if hasattr(result, 'probs') and result.probs is not None:
            probs = result.probs

            # Get top prediction
            top_class_id = probs.top1
            top_confidence = float(probs.top1conf.item())

            # Get class names
            class_names = model.names if hasattr(model, 'names') else {}
            class_name = class_names.get(top_class_id, f"Class_{top_class_id}")

            if debug_mode:
                st.write(f"- Top class ID: {top_class_id}")
                st.write(f"- Top class name: {class_name}")
                st.write(f"- Top confidence: {top_confidence:.4f}")
                st.write(f"- All class names: {class_names}")

            # SINGLE-CLASS MODEL LOGIC FOR ROAD DETECTION
            # Your model was trained ONLY on road images, so:
            # - High confidence → Model is confident this looks like the roads it was trained on
            # - Low confidence → Model is uncertain (could be non-road or unclear road)

            if debug_mode:
                st.write(f"- **SINGLE-CLASS ANALYSIS:**")
                st.write(f"  - Model trained only on: ROAD images")
                st.write(f"  - Predicted class: {class_name} (ID: {top_class_id})")
                st.write(f"  - Confidence: {top_confidence:.4f}")
                st.write(f"  - Threshold: {confidence_threshold}")

            # Check if this is the road class (should be class 0 for single-class models)
            is_road_class = (top_class_id == 0 or class_name.lower() in ['road', 'roads', 'street', 'highway'])

            if debug_mode:
                st.write(f"  - Is road class: {is_road_class}")
                st.write(f"  - Above threshold: {top_confidence >= confidence_threshold}")

            # STRICT CONFIDENCE THRESHOLDING FOR SINGLE-CLASS MODEL
            if is_road_class and top_confidence >= confidence_threshold:
                # High confidence on road class → This looks like the roads the model was trained on
                return {
                    'success': True,
                    'classification': 'road',
                    'confidence': top_confidence,
                    'class_name': class_name,
                    'class_id': top_class_id,
                    'reason': f'High confidence road detection: {top_confidence:.2%} ≥ {confidence_threshold:.2%}',
                    'threshold_applied': False,
                    'model_type': 'single_class_road'
                }
            else:
                # Low confidence OR unexpected class → I don't know
                if is_road_class:
                    reason = f'Low confidence on road: {top_confidence:.2%} < {confidence_threshold:.2%} - uncertain if this is a clear road'
                else:
                    reason = f'Unexpected class "{class_name}" - model only trained on roads, treating as unknown'

                return {
                    'success': True,
                    'classification': 'i_dont_know',
                    'confidence': top_confidence,
                    'class_name': class_name,
                    'class_id': top_class_id,
                    'reason': reason,
                    'threshold_applied': True,
                    'model_type': 'single_class_road'
                }

        else:
            return {
                'success': False,
                'classification': 'i_dont_know',
                'confidence': 0.0,
                'class_name': 'no_probs',
                'class_id': -1,
                'reason': 'Model output does not contain classification probabilities',
                'error': 'Expected classification model with probs attribute'
            }

    except Exception as e:
        if debug_mode:
            st.write(f"❌ Exception in classification: {str(e)}")

        return {
            'success': False,
            'classification': 'i_dont_know',
            'confidence': 0.0,
            'class_name': 'error',
            'class_id': -1,
            'reason': f'Classification error: {str(e)}',
            'error': str(e)
        }

def perform_inference(model, image, confidence_threshold, debug_mode=False):
    """
    Main inference function for YOLOv11 classification model

    Args:
        model: YOLOv11 classification model
        image: PIL Image
        confidence_threshold: Minimum confidence for classification
        debug_mode: Whether to show debug information

    Returns:
        dict: Classification results with strict confidence thresholding
    """
    return perform_classification_inference(model, image, confidence_threshold, debug_mode)

def main():
    # Header
    st.markdown('<h1 class="main-header">�️ Road Detection Classifier</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; color: #666; font-size: 1.2em;">Powered by YOLOv11m - Classify images as Road or No Road</p>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar for model configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Model is loaded automatically from hardcoded path
        
        # Confidence threshold
        confidence_threshold = st.slider(
            "Confidence Threshold",
            min_value=0.1,
            max_value=1.0,
            value=0.7,
            step=0.05,
            help="Minimum confidence score for valid predictions"
        )
        
        # Processing settings
        st.subheader("🚀 Processing Settings")
        st.info("Click the 'Detect Road' button to analyze uploaded images")

        # Road detection tips
        st.subheader("💡 Road Detection Tips")
        st.markdown("""
        **For better accuracy:**
        - Use clear, well-lit images
        - Include road surfaces, lanes, or vehicles
        - Avoid blurry or dark images
        - Confidence threshold: 0.5-0.8 recommended

        **Single-Class Model Logic:**
        - **ROAD**: High confidence (≥ threshold) on road class
        - **I DON'T KNOW**: Low confidence (< threshold) or non-road images
        - Model was trained ONLY on road images
        """)

        st.info("💡 **Threshold-based rejection** prevents misclassification of uncertain predictions")

        # Debug section
        st.subheader("🔧 Debug Information")
        debug_mode = st.checkbox("Show debug info", value=True)
        if debug_mode:
            st.info("Debug mode enabled - detailed information will be shown during detection")
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("📤 Image Input")
        
        # Image upload
        uploaded_image = st.file_uploader(
            "Choose an image file",
            type=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
            help="Upload an image for classification"
        )

        # Use uploaded image
        input_image = uploaded_image
        
        if input_image:
            # Display uploaded image
            image = Image.open(input_image)
            st.image(image, caption="Input Image", use_container_width=True)
            
            # Image details
            st.markdown(f"""
            <div class="metric-card">
                <strong>Image Details:</strong><br>
                Size: {image.size[0]} x {image.size[1]} pixels<br>
                Mode: {image.mode}<br>
                Format: {getattr(image, 'format', 'Unknown')}
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        st.subheader("🎯 Detection Results")
        
        # Load model
        model = None
        error = None

        if os.path.exists(MODEL_PATH):
            model, error = load_model(MODEL_PATH)

        if model is None:
            if os.path.exists(MODEL_PATH):
                st.markdown(f'<div class="error-box">❌ Failed to load model: {error if error else "Unknown error"}</div>', unsafe_allow_html=True)
            else:
                st.info("👆 Model file not found. Please ensure the model is at the specified path.")
        
        elif input_image is None:
            st.info("👆 Please upload an image or take a photo to classify.")
        
        else:
            # Manual detection button (always show)
            st.markdown("### 🎯 Ready to Analyze")
            detect_button = st.button("🚀 Detect Road", type="primary", use_container_width=True)

            # Show instructions when no detection has been run
            if not detect_button:
                st.info("👆 Click the button above to analyze the uploaded image for road detection")

            # Process image only when button is clicked
            if detect_button:
                with st.spinner("🔍 Analyzing image for road detection..."):
                    # Load and preprocess image
                    image = Image.open(input_image)
                    processed_image, preprocess_error = preprocess_image(image)
                    
                    if preprocess_error:
                        st.markdown(f'<div class="error-box">❌ Image preprocessing failed: {preprocess_error}</div>', unsafe_allow_html=True)
                    else:
                        # Perform inference with additional error handling
                        start_time = time.time()
                        try:
                            result = perform_inference(model, processed_image, confidence_threshold, debug_mode)
                            inference_time = time.time() - start_time
                        except Exception as e:
                            inference_time = time.time() - start_time
                            result = {
                                'success': False,
                                'error': f'Error during inference: {str(e)}',
                                'classification': 'i_dont_know',
                                'confidence': 0.0,
                                'reason': f'Unexpected error: {str(e)}'
                            }
                            st.error(f"❌ Error occurred during detection.")

                        # Display results
                        if not result.get('success', False) and 'error' in result:
                            st.markdown(f'<div class="error-box">❌ Inference failed: {result["error"]}</div>', unsafe_allow_html=True)
                            # Show classification as unknown for errors
                            st.markdown(f"""
                            <div class="error-box">
                                <h3>❓ UNKNOWN</h3>
                                <p>Due to inference error, classification uncertain.</p>
                            </div>
                            """, unsafe_allow_html=True)
                        else:
                            # Get classification result
                            classification = result.get('classification', 'unknown')
                            confidence = result.get('confidence', 0.0)
                            reason = result.get('reason', 'No reason provided')

                            # Display road/no-road/unknown classification prominently
                            if classification == 'road':
                                st.markdown(f"""
                                <div class="success-box">
                                    <h3>🛣️ ROAD DETECTED</h3>
                                    <p><strong>Classification:</strong> ROAD</p>
                                    <p><strong>Confidence:</strong> {confidence:.2%}</p>
                                    <p><strong>Reason:</strong> {reason}</p>
                                </div>
                                """, unsafe_allow_html=True)
                            else:  # i_dont_know classification
                                st.markdown(f"""
                                <div class="unknown-box">
                                    <h3>🤷‍♂️ I DON'T KNOW</h3>
                                    <p><strong>Classification:</strong> I DON'T KNOW</p>
                                    <p><strong>Confidence:</strong> {confidence:.2%}</p>
                                    <p><strong>Reason:</strong> {reason}</p>
                                    <p><em>This doesn't look like the road images I was trained on, or I'm not confident enough.</em></p>
                                </div>
                                """, unsafe_allow_html=True)

                            if result.get('success', False):
                                # Detailed detection information
                                with st.expander("🔍 Detailed Classification Results", expanded=False):
                                    st.markdown(f"""
                                    **YOLOv11 Classification Results:**
                                    - **Predicted Class:** {result.get('class_name', 'unknown')}
                                    - **Confidence:** {result.get('confidence', 0.0):.4f}
                                    - **Class ID:** {result.get('class_id', -1)}
                                    - **Confidence Threshold:** {confidence_threshold}
                                    - **Threshold Applied:** {'Yes' if result.get('threshold_applied', False) else 'No'}
                                    - **Reason:** {reason}
                                    """)

                                    # Show model information
                                    if hasattr(model, 'names'):
                                        st.markdown(f"""
                                        **Model Information:**
                                        - **Model Classes:** {model.names}
                                        - **Model Task:** {getattr(model, 'task', 'Unknown')}
                                        """)

                                    # Show confidence analysis
                                    if result.get('threshold_applied', False):
                                        st.warning(f"⚠️ **Confidence below threshold**: The model predicted '{result.get('class_name', 'unknown')}' with {result.get('confidence', 0.0):.2%} confidence, but this is below the {confidence_threshold:.2%} threshold, so it's classified as UNKNOWN to prevent false positives.")
                                    else:
                                        st.success(f"✅ **High confidence prediction**: The model is confident in its prediction with {result.get('confidence', 0.0):.2%} confidence.")

                            # Enhanced metrics
                            col_a, col_b, col_c = st.columns(3)
                            with col_a:
                                st.metric("Classification", classification.upper().replace('_', ' '))
                            with col_b:
                                st.metric("Confidence", f"{confidence:.3f}")
                            with col_c:
                                st.metric("Inference Time", f"{inference_time:.3f}s")

                            # Confidence bar with color coding
                            if classification == 'road':
                                st.success(f"Road Detection Confidence: {confidence:.2%}")
                            else:  # i_dont_know
                                st.warning(f"Uncertain Classification - Confidence: {confidence:.2%}")

                            st.progress(confidence)
        
        # No cleanup needed for hardcoded model path
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666;">
        <p>🛣️ Road Detection Classifier | Built with Streamlit & Ultralytics YOLOv11m</p>
        <p>💡 <em>Tip: Upload clear images with visible roads, lanes, or vehicles for best results</em></p>
        <p>🎯 <em>Optimized for Road vs No-Road binary classification</em></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()